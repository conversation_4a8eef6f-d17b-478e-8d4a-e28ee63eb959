import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import shutil
from datetime import datetime

class NNUNetConfigGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Configuration nnU-Net Scripts")
        self.root.geometry("800x700")
        
        # Configuration par défaut
        self.config = {
            "train": {
                "DATASET_ID": "017",
                "CONFIGURATION": "3d_fullres",
                "FOLD": "all",
                "PLANS_NAME": "nnUNetPlans",
                "EPOCHS": 5,
                "GPU_ID": "0",
                "NUM_GPUS": 1,
                "SAVE_NPZ": True,
                "CONTINUE_TRAINING": False
            },
            "infer": {
                "DATASET_ID": "018",
                "CONFIGURATION": "2d",
                "FOLD": "all",
                "PLANS_NAME": "nnUNetPlans",
                "EPOCHS": 5,
                "GPU_ID": "0",
                "INPUT_FOLDER": r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Dataset\imagesTestInference",
                "BASE_OUTPUT_ROOT": r"C:\Users\<USER>\OneDrive - EvidentScientific\Documents\4Corrosion\Results\inference"
            },
            "export": {
                "DATASET_ID": "014",
                "CONFIGURATION": "2d",
                "FOLD": "all",
                "TRAINER_NAME": "nnUNetTrainer_5epochs",
                "PLANS_NAME": "nnUNetPlans"
            }
        }
        
        self.load_config()
        self.create_widgets()
    
    def create_widgets(self):
        # Notebook pour les onglets
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Onglet Train
        train_frame = ttk.Frame(notebook)
        notebook.add(train_frame, text="Train")
        self.create_train_tab(train_frame)
        
        # Onglet Infer
        infer_frame = ttk.Frame(notebook)
        notebook.add(infer_frame, text="Infer")
        self.create_infer_tab(infer_frame)
        
        # Onglet Export
        export_frame = ttk.Frame(notebook)
        notebook.add(export_frame, text="Export")
        self.create_export_tab(export_frame)
        
        # Boutons principaux
        button_frame = ttk.Frame(self.root)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Button(button_frame, text="Sauvegarder Config", command=self.save_config).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Appliquer aux Scripts", command=self.apply_to_scripts).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Exécuter Train", command=self.run_train).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Exécuter Infer", command=self.run_infer).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Exécuter Export", command=self.run_export).pack(side="left", padx=5)
    
    def create_train_tab(self, parent):
        # Configuration générale
        general_frame = ttk.LabelFrame(parent, text="Configuration Générale")
        general_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(general_frame, text="Dataset ID:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.train_dataset_id = ttk.Entry(general_frame)
        self.train_dataset_id.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.train_dataset_id.insert(0, self.config["train"]["DATASET_ID"])
        
        ttk.Label(general_frame, text="Configuration:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.train_config = ttk.Combobox(general_frame, values=["2d", "3d_fullres"])
        self.train_config.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.train_config.set(self.config["train"]["CONFIGURATION"])
        
        ttk.Label(general_frame, text="Fold:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.train_fold = ttk.Combobox(general_frame, values=["all", "0", "1", "2", "3", "4"])
        self.train_fold.grid(row=2, column=1, sticky="ew", padx=5, pady=2)
        self.train_fold.set(self.config["train"]["FOLD"])
        
        ttk.Label(general_frame, text="Plans Name:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.train_plans = ttk.Entry(general_frame)
        self.train_plans.grid(row=3, column=1, sticky="ew", padx=5, pady=2)
        self.train_plans.insert(0, self.config["train"]["PLANS_NAME"])
        
        ttk.Label(general_frame, text="Epochs:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.train_epochs = ttk.Spinbox(general_frame, from_=1, to=1000, value=self.config["train"]["EPOCHS"])
        self.train_epochs.grid(row=4, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(general_frame, text="GPU ID:").grid(row=5, column=0, sticky="w", padx=5, pady=2)
        self.train_gpu_id = ttk.Entry(general_frame)
        self.train_gpu_id.grid(row=5, column=1, sticky="ew", padx=5, pady=2)
        self.train_gpu_id.insert(0, self.config["train"]["GPU_ID"])
        
        ttk.Label(general_frame, text="Num GPUs:").grid(row=6, column=0, sticky="w", padx=5, pady=2)
        self.train_num_gpus = ttk.Spinbox(general_frame, from_=1, to=8, value=self.config["train"]["NUM_GPUS"])
        self.train_num_gpus.grid(row=6, column=1, sticky="ew", padx=5, pady=2)
        
        general_frame.columnconfigure(1, weight=1)
        
        # Hyperparamètres
        hyper_frame = ttk.LabelFrame(parent, text="Hyperparamètres")
        hyper_frame.pack(fill="x", padx=5, pady=5)
        
        self.train_save_npz = tk.BooleanVar(value=self.config["train"]["SAVE_NPZ"])
        ttk.Checkbutton(hyper_frame, text="Save NPZ", variable=self.train_save_npz).pack(anchor="w", padx=5, pady=2)
        
        self.train_continue = tk.BooleanVar(value=self.config["train"]["CONTINUE_TRAINING"])
        ttk.Checkbutton(hyper_frame, text="Continue Training", variable=self.train_continue).pack(anchor="w", padx=5, pady=2)
    
    def create_infer_tab(self, parent):
        # Configuration
        config_frame = ttk.LabelFrame(parent, text="Configuration")
        config_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(config_frame, text="Dataset ID:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.infer_dataset_id = ttk.Entry(config_frame)
        self.infer_dataset_id.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.infer_dataset_id.insert(0, self.config["infer"]["DATASET_ID"])
        
        ttk.Label(config_frame, text="Configuration:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.infer_config = ttk.Combobox(config_frame, values=["2d", "3d_fullres"])
        self.infer_config.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.infer_config.set(self.config["infer"]["CONFIGURATION"])
        
        ttk.Label(config_frame, text="Fold:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.infer_fold = ttk.Combobox(config_frame, values=["all", "0", "1", "2", "3", "4"])
        self.infer_fold.grid(row=2, column=1, sticky="ew", padx=5, pady=2)
        self.infer_fold.set(self.config["infer"]["FOLD"])
        
        ttk.Label(config_frame, text="Plans Name:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.infer_plans = ttk.Entry(config_frame)
        self.infer_plans.grid(row=3, column=1, sticky="ew", padx=5, pady=2)
        self.infer_plans.insert(0, self.config["infer"]["PLANS_NAME"])
        
        ttk.Label(config_frame, text="Epochs:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.infer_epochs = ttk.Spinbox(config_frame, from_=0, to=1000, value=self.config["infer"]["EPOCHS"])
        self.infer_epochs.grid(row=4, column=1, sticky="ew", padx=5, pady=2)
        
        ttk.Label(config_frame, text="GPU ID:").grid(row=5, column=0, sticky="w", padx=5, pady=2)
        self.infer_gpu_id = ttk.Entry(config_frame)
        self.infer_gpu_id.grid(row=5, column=1, sticky="ew", padx=5, pady=2)
        self.infer_gpu_id.insert(0, self.config["infer"]["GPU_ID"])
        
        config_frame.columnconfigure(1, weight=1)
        
        # Dossiers
        folders_frame = ttk.LabelFrame(parent, text="Dossiers")
        folders_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(folders_frame, text="Input Folder:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.infer_input = ttk.Entry(folders_frame)
        self.infer_input.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.infer_input.insert(0, self.config["infer"]["INPUT_FOLDER"])
        ttk.Button(folders_frame, text="Browse", command=lambda: self.browse_folder(self.infer_input)).grid(row=0, column=2, padx=5, pady=2)
        
        ttk.Label(folders_frame, text="Output Root:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.infer_output = ttk.Entry(folders_frame)
        self.infer_output.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.infer_output.insert(0, self.config["infer"]["BASE_OUTPUT_ROOT"])
        ttk.Button(folders_frame, text="Browse", command=lambda: self.browse_folder(self.infer_output)).grid(row=1, column=2, padx=5, pady=2)
        
        folders_frame.columnconfigure(1, weight=1)
    
    def create_export_tab(self, parent):
        # Configuration
        config_frame = ttk.LabelFrame(parent, text="Configuration")
        config_frame.pack(fill="x", padx=5, pady=5)
        
        ttk.Label(config_frame, text="Dataset ID:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.export_dataset_id = ttk.Entry(config_frame)
        self.export_dataset_id.grid(row=0, column=1, sticky="ew", padx=5, pady=2)
        self.export_dataset_id.insert(0, self.config["export"]["DATASET_ID"])
        
        ttk.Label(config_frame, text="Configuration:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.export_config = ttk.Combobox(config_frame, values=["2d", "3d_fullres"])
        self.export_config.grid(row=1, column=1, sticky="ew", padx=5, pady=2)
        self.export_config.set(self.config["export"]["CONFIGURATION"])
        
        ttk.Label(config_frame, text="Fold:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.export_fold = ttk.Combobox(config_frame, values=["all", "0", "1", "2", "3", "4"])
        self.export_fold.grid(row=2, column=1, sticky="ew", padx=5, pady=2)
        self.export_fold.set(self.config["export"]["FOLD"])
        
        ttk.Label(config_frame, text="Trainer Name:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.export_trainer = ttk.Entry(config_frame)
        self.export_trainer.grid(row=3, column=1, sticky="ew", padx=5, pady=2)
        self.export_trainer.insert(0, self.config["export"]["TRAINER_NAME"])
        
        ttk.Label(config_frame, text="Plans Name:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.export_plans = ttk.Entry(config_frame)
        self.export_plans.grid(row=4, column=1, sticky="ew", padx=5, pady=2)
        self.export_plans.insert(0, self.config["export"]["PLANS_NAME"])
        
        config_frame.columnconfigure(1, weight=1)
    
    def browse_folder(self, entry_widget):
        folder = filedialog.askdirectory()
        if folder:
            entry_widget.delete(0, tk.END)
            entry_widget.insert(0, folder)
    
    def get_current_config(self):
        return {
            "train": {
                "DATASET_ID": self.train_dataset_id.get(),
                "CONFIGURATION": self.train_config.get(),
                "FOLD": self.train_fold.get(),
                "PLANS_NAME": self.train_plans.get(),
                "EPOCHS": int(self.train_epochs.get()),
                "GPU_ID": self.train_gpu_id.get(),
                "NUM_GPUS": int(self.train_num_gpus.get()),
                "SAVE_NPZ": self.train_save_npz.get(),
                "CONTINUE_TRAINING": self.train_continue.get()
            },
            "infer": {
                "DATASET_ID": self.infer_dataset_id.get(),
                "CONFIGURATION": self.infer_config.get(),
                "FOLD": self.infer_fold.get(),
                "PLANS_NAME": self.infer_plans.get(),
                "EPOCHS": int(self.infer_epochs.get()),
                "GPU_ID": self.infer_gpu_id.get(),
                "INPUT_FOLDER": self.infer_input.get(),
                "BASE_OUTPUT_ROOT": self.infer_output.get()
            },
            "export": {
                "DATASET_ID": self.export_dataset_id.get(),
                "CONFIGURATION": self.export_config.get(),
                "FOLD": self.export_fold.get(),
                "TRAINER_NAME": self.export_trainer.get(),
                "PLANS_NAME": self.export_plans.get()
            }
        }
    
    def save_config(self):
        self.config = self.get_current_config()
        with open("nnunet_config.json", "w") as f:
            json.dump(self.config, f, indent=2)
        messagebox.showinfo("Succès", "Configuration sauvegardée dans nnunet_config.json")
    
    def load_config(self):
        if os.path.exists("nnunet_config.json"):
            try:
                with open("nnunet_config.json", "r") as f:
                    loaded_config = json.load(f)
                    # Mettre à jour la configuration avec les valeurs chargées
                    for script in self.config:
                        if script in loaded_config:
                            self.config[script].update(loaded_config[script])
            except Exception as e:
                messagebox.showwarning("Avertissement", f"Erreur lors du chargement de la configuration: {e}")

    def backup_script(self, script_name):
        """Créer une sauvegarde du script avant modification"""
        if os.path.exists(script_name):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{script_name}.backup_{timestamp}"
            shutil.copy2(script_name, backup_name)
            return backup_name
        return None

    def apply_to_scripts(self):
        """Appliquer la configuration aux scripts existants"""
        current_config = self.get_current_config()

        try:
            # Backup et modification de train_nnunet.py
            backup_train = self.backup_script("train_nnunet.py")
            self.update_train_script(current_config["train"])

            # Backup et modification de infer_nnunet.py
            backup_infer = self.backup_script("infer_nnunet.py")
            self.update_infer_script(current_config["infer"])

            # Backup et modification de export_model_zip.py
            backup_export = self.backup_script("export_model_zip.py")
            self.update_export_script(current_config["export"])

            message = "Scripts mis à jour avec succès!\n\nSauvegardes créées:\n"
            if backup_train:
                message += f"- {backup_train}\n"
            if backup_infer:
                message += f"- {backup_infer}\n"
            if backup_export:
                message += f"- {backup_export}\n"

            messagebox.showinfo("Succès", message)

        except Exception as e:
            messagebox.showerror("Erreur", f"Erreur lors de la mise à jour des scripts: {e}")

    def update_train_script(self, config):
        """Mettre à jour le script train_nnunet.py"""
        with open("train_nnunet.py", "r", encoding="utf-8") as f:
            content = f.read()

        # Remplacer les valeurs de configuration
        replacements = [
            (r'DATASET_ID = "[^"]*"', f'DATASET_ID = "{config["DATASET_ID"]}"'),
            (r'CONFIGURATION = "[^"]*"', f'CONFIGURATION = "{config["CONFIGURATION"]}"'),
            (r'FOLD = "[^"]*"', f'FOLD = "{config["FOLD"]}"'),
            (r'PLANS_NAME = "[^"]*"', f'PLANS_NAME = "{config["PLANS_NAME"]}"'),
            (r'EPOCHS = \d+', f'EPOCHS = {config["EPOCHS"]}'),
            (r'GPU_ID = "[^"]*"', f'GPU_ID = "{config["GPU_ID"]}"'),
            (r'NUM_GPUS = \d+', f'NUM_GPUS = {config["NUM_GPUS"]}'),
            (r'SAVE_NPZ = (True|False)', f'SAVE_NPZ = {config["SAVE_NPZ"]}'),
            (r'CONTINUE_TRAINING = (True|False)', f'CONTINUE_TRAINING = {config["CONTINUE_TRAINING"]}')
        ]

        import re
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)

        with open("train_nnunet.py", "w", encoding="utf-8") as f:
            f.write(content)

    def update_infer_script(self, config):
        """Mettre à jour le script infer_nnunet.py"""
        with open("infer_nnunet.py", "r", encoding="utf-8") as f:
            content = f.read()

        # Remplacer les valeurs de configuration
        replacements = [
            (r'DATASET_ID = "[^"]*"', f'DATASET_ID = "{config["DATASET_ID"]}"'),
            (r'CONFIGURATION = "[^"]*"', f'CONFIGURATION = "{config["CONFIGURATION"]}"'),
            (r'FOLD = "[^"]*"', f'FOLD = "{config["FOLD"]}"'),
            (r'PLANS_NAME = "[^"]*"', f'PLANS_NAME = "{config["PLANS_NAME"]}"'),
            (r'EPOCHS = \d+', f'EPOCHS = {config["EPOCHS"]}'),
            (r'GPU_ID = "[^"]*"', f'GPU_ID = "{config["GPU_ID"]}"'),
            (r'INPUT_FOLDER = r"[^"]*"', f'INPUT_FOLDER = r"{config["INPUT_FOLDER"]}"'),
            (r'BASE_OUTPUT_ROOT = r"[^"]*"', f'BASE_OUTPUT_ROOT = r"{config["BASE_OUTPUT_ROOT"]}"')
        ]

        import re
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)

        with open("infer_nnunet.py", "w", encoding="utf-8") as f:
            f.write(content)

    def update_export_script(self, config):
        """Mettre à jour le script export_model_zip.py"""
        with open("export_model_zip.py", "r", encoding="utf-8") as f:
            content = f.read()

        # Remplacer les valeurs de configuration
        replacements = [
            (r'DATASET_ID = "[^"]*"', f'DATASET_ID = "{config["DATASET_ID"]}"'),
            (r'CONFIGURATION = "[^"]*"', f'CONFIGURATION = "{config["CONFIGURATION"]}"'),
            (r'FOLD = "[^"]*"', f'FOLD = "{config["FOLD"]}"'),
            (r'TRAINER_NAME = "[^"]*"', f'TRAINER_NAME = "{config["TRAINER_NAME"]}"'),
            (r'PLANS_NAME = "[^"]*"', f'PLANS_NAME = "{config["PLANS_NAME"]}"')
        ]

        import re
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)

        with open("export_model_zip.py", "w", encoding="utf-8") as f:
            f.write(content)

    def run_train(self):
        """Exécuter le script train_nnunet.py"""
        self.apply_to_scripts()  # Appliquer la config d'abord
        self.run_script("train_nnunet.py")

    def run_infer(self):
        """Exécuter le script infer_nnunet.py"""
        self.apply_to_scripts()  # Appliquer la config d'abord
        self.run_script("infer_nnunet.py")

    def run_export(self):
        """Exécuter le script export_model_zip.py"""
        self.apply_to_scripts()  # Appliquer la config d'abord
        self.run_script("export_model_zip.py")

    def run_script(self, script_name):
        """Exécuter un script Python dans le terminal"""
        import subprocess
        import threading

        def run_in_thread():
            try:
                # Lancer le script dans un nouveau terminal
                if os.name == 'nt':  # Windows
                    subprocess.Popen(f'start cmd /k "python {script_name}"', shell=True)
                else:  # Linux/Mac
                    subprocess.Popen(f'gnome-terminal -- python {script_name}', shell=True)

                messagebox.showinfo("Script lancé", f"Le script {script_name} a été lancé dans un nouveau terminal.")
            except Exception as e:
                messagebox.showerror("Erreur", f"Erreur lors du lancement du script: {e}")

        # Lancer dans un thread séparé pour ne pas bloquer l'interface
        thread = threading.Thread(target=run_in_thread)
        thread.daemon = True
        thread.start()


def main():
    root = tk.Tk()
    app = NNUNetConfigGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
